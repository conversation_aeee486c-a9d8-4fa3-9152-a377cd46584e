#!/bin/bash

# Professional SkyWars Arena Deployment Script
# This script deploys a complete professional SkyWars arena

echo "=========================================="
echo "  PROFESSIONAL SKYWARS ARENA DEPLOYMENT  "
echo "=========================================="

# Check if server is running
if ! docker ps | grep -q minecraft; then
    echo "Error: Minecraft server is not running!"
    echo "Please start the server first with: docker-compose up -d"
    exit 1
fi

echo "Step 1: Creating SkyWars world..."
# Create the SkyWars world
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "mv create skywars_arena1 normal"
sleep 5

echo "Step 2: Setting up world properties..."
# Configure world settings
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "mv modify set generator skywars_arena1 void"
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "mv modify set difficulty skywars_arena1 hard"
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "mv modify set pvp skywars_arena1 true"
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "mv modify set animals skywars_arena1 false"
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "mv modify set monsters skywars_arena1 false"

echo "Step 3: Building professional arena structure..."
# Run the arena setup script
chmod +x scripts/setup-skywars-arena.sh
./scripts/setup-skywars-arena.sh

echo "Step 4: Configuring SkyWars plugin..."
# Restart SkyWars plugin to load new configuration
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "plugman reload SkyWars"
sleep 3

echo "Step 5: Setting up arena permissions..."
# Configure permissions for SkyWars
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "lp group default permission set skywars.join true"
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "lp group default permission set skywars.leave true"
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "lp group default permission set skywars.spectate true"

echo "Step 6: Creating lobby teleport NPC..."
# Add SkyWars NPC to lobby
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "mv tp lobby"
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "citizens create SkyWars_Portal npc"
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "npc select SkyWars_Portal"
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "npc skin SkyWars_Portal notch"
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "npc addcmd SkyWars_Portal sw join arena1 --permission skywars.join"

echo "Step 7: Testing arena functionality..."
# Test arena setup
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "sw list"
docker exec minecraft-server rcon-cli -H localhost -p 25575 -P minecraft_rcon "sw info arena1"

echo "=========================================="
echo "  PROFESSIONAL SKYWARS DEPLOYMENT COMPLETE!"
echo "=========================================="
echo ""
echo "Arena Features:"
echo "✓ Multi-level center island with premium loot"
echo "✓ 8 themed spawn islands (Forest, Desert, Mountain, Nether, Ocean, Ice, End, Jungle)"
echo "✓ 4 floating premium loot islands"
echo "✓ Professional world border and death barriers"
echo "✓ Enhanced chest loot tables"
echo "✓ Lobby NPC for easy access"
echo ""
echo "How to play:"
echo "1. Players can join from lobby by clicking the SkyWars NPC"
echo "2. Or use command: /sw join arena1"
echo "3. Minimum 2 players, maximum 8 players"
echo "4. 30-second grace period before PvP starts"
echo "5. World border shrinks after 5 minutes"
echo "6. Deathmatch forced after 10 minutes"
echo ""
echo "Admin commands:"
echo "- /sw list                 - List all arenas"
echo "- /sw info arena1          - Arena information"
echo "- /sw forcestart arena1    - Force start game"
echo "- /sw stop arena1          - Stop current game"
echo "- /sw reload               - Reload plugin config"
echo ""
echo "Arena is ready for epic sky battles! 🏹⚔️"
