#!/bin/bash

# Simple build script for LobbyTeleport plugin

echo "Building LobbyTeleport plugin..."

# Create directories
mkdir -p target/classes
mkdir -p target

# Copy plugin.yml to target
cp plugin.yml target/classes/

# Find Spigot/Paper jar for compilation
SPIGOT_JAR=""
if [ -f "/data/paper-1.21.4-232.jar" ]; then
    SPIGOT_JAR="/data/paper-1.21.4-232.jar"
elif [ -f "../../../data/paper-1.21.4-232.jar" ]; then
    SPIGOT_JAR="../../../data/paper-1.21.4-232.jar"
else
    echo "Error: Could not find Paper jar file for compilation"
    exit 1
fi

echo "Using Paper jar: $SPIGOT_JAR"

# Compile Java source
javac -cp "$SPIGOT_JAR" -d target/classes src/main/java/com/minecraft/lobbyteleport/*.java

if [ $? -eq 0 ]; then
    echo "Compilation successful!"
    
    # Create JAR file
    cd target/classes
    jar cf ../LobbyTeleport.jar .
    cd ../..
    
    echo "Plugin JAR created: target/LobbyTeleport.jar"
    
    # Copy to plugins directory if it exists
    if [ -d "../../" ]; then
        cp target/LobbyTeleport.jar ../../LobbyTeleport.jar
        echo "Plugin copied to plugins directory"
    fi
else
    echo "Compilation failed!"
    exit 1
fi
