package com.minecraft.lobbyteleport;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerRespawnEvent;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;

public class LobbyTeleport extends JavaPlugin implements Listener {
    
    private Location lobbySpawn;
    
    @Override
    public void onEnable() {
        // Register events
        getServer().getPluginManager().registerEvents(this, this);
        
        // Set up lobby spawn location
        setupLobbySpawn();
        
        getLogger().info("LobbyTeleport plugin enabled! Players will be teleported to lobby on login.");
    }
    
    @Override
    public void onDisable() {
        getLogger().info("LobbyTeleport plugin disabled!");
    }
    
    private void setupLobbySpawn() {
        World lobbyWorld = Bukkit.getWorld("lobby");
        if (lobbyWorld != null) {
            lobbySpawn = new Location(lobbyWorld, 0.5, 65.0, 0.5, 0.0f, 0.0f);
            getLogger().info("Lobby spawn set to: " + lobbySpawn.toString());
        } else {
            getLogger().warning("Lobby world not found! Plugin may not work correctly.");
        }
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Delay teleportation to ensure player is fully loaded
        new BukkitRunnable() {
            @Override
            public void run() {
                teleportToLobby(player);
            }
        }.runTaskLater(this, 20L); // Wait 1 second (20 ticks)
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerRespawn(PlayerRespawnEvent event) {
        Player player = event.getPlayer();
        
        // Check if player is not in a specific game world
        String worldName = player.getWorld().getName();
        if (!worldName.equals("world") && !worldName.startsWith("skywars")) {
            if (lobbySpawn != null) {
                event.setRespawnLocation(lobbySpawn);
            }
        }
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (command.getName().equalsIgnoreCase("lobby")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                teleportToLobby(player);
                return true;
            } else {
                sender.sendMessage("This command can only be used by players!");
                return true;
            }
        }
        return false;
    }
    
    private void teleportToLobby(Player player) {
        if (lobbySpawn == null) {
            setupLobbySpawn();
        }
        
        if (lobbySpawn != null) {
            player.teleport(lobbySpawn);
            player.sendMessage("§aWelcome to the lobby!");
            getLogger().info("Teleported " + player.getName() + " to lobby spawn.");
        } else {
            player.sendMessage("§cError: Lobby spawn not found!");
            getLogger().warning("Failed to teleport " + player.getName() + " - lobby spawn not set!");
        }
    }
}
